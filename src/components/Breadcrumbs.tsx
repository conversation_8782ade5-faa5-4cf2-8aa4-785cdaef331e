import {ChevronRight} from 'lucide-react';
import Link from 'next/link';
import React from 'react';

type BreadcrumbItem = {
  label: string;
  href?: React.ComponentProps<typeof Link>['href'];
};

type BreadcrumbsProps = {
  items: BreadcrumbItem[];
};

export function Breadcrumbs({items}: BreadcrumbsProps) {
  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && <ChevronRight className="h-4 w-4" />}
          {item.href
            ? (
                <Link href={item.href} className="hover:text-foreground transition-colors">
                  {item.label}
                </Link>
              )
            : (
                <span className="text-foreground font-medium">{item.label}</span>
              )}
        </React.Fragment>
      ))}
    </nav>
  );
}
